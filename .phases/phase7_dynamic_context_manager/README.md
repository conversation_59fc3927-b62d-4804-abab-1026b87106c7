# Enhanced TTL Question-Answering System with Dynamic Context Manager

## Overview

This is an advanced agentic system that significantly enhances the Phase 5 TTL QA system by implementing a **Dynamic Context Manager**. The system intelligently extracts and utilizes contextual information from ontology definitions and guidelines to provide more accurate, constraint-aware SPARQL query generation and analysis.

## Key Enhancements

### 🧠 Dynamic Context Management
- **Ontology Analysis**: Automatically extracts class relationships, property domain/range constraints, and hierarchical structures
- **Guidelines Processing**: Parses property definitions, validation rules, data types, and unit constraints
- **Context-Aware Query Generation**: Uses extracted context to generate SPARQL queries that respect ontological and guideline constraints

### 🔧 Enhanced Agent Tools

The system provides several intelligent agent tools:

1. **`extract_ontology_context`**: Identifies relevant ontology constraints for a given question
2. **`extract_guidelines_context`**: Extracts applicable guidelines and validation rules
3. **`develop_contextual_strategy`**: Creates question-answering strategies considering all constraints
4. **`generate_enhanced_sparql_query`**: Generates context-compliant SPARQL queries
5. **`execute_sparql_query`**: Executes queries against the TTL data
6. **`analyze_contextual_results`**: Analyzes results with full contextual awareness

### 📊 Context Sources

#### Ontology (ontology.ttl)
- **Class Relationships**: Defines how different classes relate to each other
- **Property Constraints**: Specifies domain and range constraints for properties
- **Hierarchical Structure**: Establishes class hierarchies and inheritance

#### Guidelines (guideline.json)
- **Property Definitions**: Detailed descriptions and metadata for each property
- **Validation Rules**: Min/max values, required fields, data type constraints
- **Data Types**: Specifies whether properties are strings, integers, floats, etc.
- **Unit Information**: Defines measurement units and abbreviations

## Architecture

```
User Question
     ↓
Context Extraction (Ontology + Guidelines)
     ↓
Contextual Strategy Development
     ↓
Enhanced SPARQL Query Generation
     ↓
Query Execution & Validation
     ↓
Contextual Result Analysis
     ↓
Comprehensive Answer
```

## Files Structure

```
phase7_dynamic_context_manager/
├── dynamic_context_manager.py    # Main enhanced system
├── test_enhanced_system.py       # Comprehensive test suite
├── README.md                     # This documentation
└── assets/
    ├── example.ttl               # Sample TTL data
    ├── ontology.ttl              # Ontology definitions
    └── guideline.json            # Guidelines and validation rules
```

## Usage

### Interactive CLI Mode

```bash
python dynamic_context_manager.py
```

The CLI provides:
- **load**: Load TTL file with ontology and guidelines
- **ask**: Ask questions about the data with full context awareness
- **context**: View current context information
- **quit**: Exit the application

### Comprehensive Testing

```bash
python test_enhanced_system.py
```

This runs a comprehensive test suite with 8 different question types:
1. Basic Counting
2. Property-based Filtering
3. Location-based Queries
4. Complex Relationships
5. Data Type Validation
6. Multi-entity Analysis
7. Constraint-based Queries
8. Geographic Distribution

## Example Context Usage

### Question: "How many buildings have parking spaces?"

**Context Extraction:**
- **Ontology**: Identifies Building class and its relationships
- **Guidelines**: Validates `parking-spaces` property as integer type with specific constraints

**Enhanced Query Generation:**
```sparql
PREFIX ibpdi: <https://ibpdi.datacat.org/class/>
PREFIX prop: <https://ibpdi.datacat.org/property/>

SELECT (COUNT(?building) as ?count) WHERE {
    ?building a ibpdi:Building .
    ?building prop:parking-spaces ?spaces .
    FILTER(?spaces > 0)
}
```

**Context-Aware Analysis:**
- Validates that the query respects ontology domain constraints
- Confirms property usage follows guidelines data type specifications
- Provides insights about data completeness and constraint compliance

## Key Benefits

### 🎯 Improved Accuracy
- Queries respect ontological constraints, reducing errors
- Property usage follows guidelines specifications
- Data type validation prevents type mismatches

### 🔍 Enhanced Understanding
- Provides explanations of how context influenced query generation
- Shows compliance with ontology and guidelines constraints
- Offers insights into data structure and relationships

### 🚀 Intelligent Reasoning
- Leverages class relationships for complex multi-entity queries
- Considers validation rules when filtering data
- Adapts query strategies based on available context

### 📈 Better User Experience
- Clear explanations of reasoning process
- Verbose output showing context usage
- Comprehensive error handling with constraint explanations

## Technical Implementation

### Context Analysis Pipeline

1. **Ontology Parsing**: Extracts RDF triples to build relationship maps
2. **Guidelines Processing**: Parses JSON structure to extract validation rules
3. **Context Integration**: Combines both sources into unified context objects
4. **Dynamic Extraction**: Selects relevant context based on question analysis

### Enhanced Agent Architecture

- **Multi-Agent System**: Specialized agents for different aspects (SPARQL generation, analysis)
- **Context-Aware Prompting**: Provides relevant context to each agent
- **Validation Pipeline**: Ensures generated queries comply with all constraints
- **Iterative Refinement**: Can generate follow-up queries based on initial results

## Comparison with Phase 5

| Aspect | Phase 5 | Phase 7 (Enhanced) |
|--------|---------|-------------------|
| Context Awareness | Basic TTL analysis only | Full ontology + guidelines |
| Query Generation | Template-based | Context-aware with constraints |
| Validation | Syntax only | Syntax + semantic + guidelines |
| Error Handling | Basic | Constraint-aware explanations |
| Result Analysis | Simple statistics | Contextual compliance analysis |
| User Feedback | Limited | Comprehensive with reasoning |

## Future Enhancements

- **Machine Learning Integration**: Learn from user feedback to improve context extraction
- **Advanced Reasoning**: Implement more sophisticated ontological reasoning
- **Performance Optimization**: Cache context analysis for faster query generation
- **Extended Guidelines**: Support for more complex validation rules and constraints
- **Multi-language Support**: Extend to other RDF serialization formats

## Dependencies

- `pydantic-ai`: For agent framework and model integration
- `rdflib`: For RDF/TTL parsing and SPARQL execution
- `rich`: For beautiful CLI interface
- `openrouter`: For LLM API access

## Getting Started

1. Ensure you have the required API key set in your environment:
   ```bash
   export OR_API_KEY="your-openrouter-api-key"
   ```

2. Install dependencies:
   ```bash
   pip install pydantic-ai rdflib rich python-dotenv
   ```

3. Run the interactive system:
   ```bash
   python dynamic_context_manager.py
   ```

4. Or run the comprehensive test suite:
   ```bash
   python test_enhanced_system.py
   ```

The system will demonstrate how ontology and guidelines context dramatically improves the accuracy and usefulness of TTL question-answering!
