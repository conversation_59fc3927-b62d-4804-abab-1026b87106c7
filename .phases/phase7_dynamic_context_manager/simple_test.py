#!/usr/bin/env python3
"""
Simple test to verify the enhanced system works
"""

import asyncio
import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel

# Import our enhanced system
from dynamic_context_manager import (
    EnhancedTTLContext, 
    analyze_ttl_file_basic,
    analyze_ontology,
    analyze_guidelines,
    enhanced_sparql_agent
)
from rdflib import Graph

async def simple_test():
    """Simple test of the enhanced system"""
    console = Console()
    
    console.print(Panel(
        "[bold cyan]Simple Enhanced System Test[/bold cyan]",
        border_style="blue"
    ))
    
    # Load test context
    console.print("Loading test context...")
    
    ttl_path = "assets/example.ttl"
    ontology_path = "assets/ontology.ttl"
    guidelines_path = "assets/guideline.json"
    
    # Load TTL graph
    graph = Graph()
    graph.parse(ttl_path, format='turtle')
    
    # Analyze contexts
    ontology_context = analyze_ontology(ontology_path)
    guidelines_context = analyze_guidelines(guidelines_path)
    basic_analysis = analyze_ttl_file_basic(ttl_path)
    
    # Create enhanced context
    context = EnhancedTTLContext(
        graph=graph,
        file_path=ttl_path,
        basic_analysis=basic_analysis,
        ontology_context=ontology_context,
        guidelines_context=guidelines_context
    )
    
    console.print(f"✅ Context loaded: {len(graph)} triples")
    console.print(f"📊 Ontology: {len(ontology_context.class_relationships)} class relationships")
    console.print(f"📋 Guidelines: {len(guidelines_context.property_definitions)} property definitions")
    
    # Test enhanced SPARQL generation
    console.print("\n[bold yellow]Testing Enhanced SPARQL Generation...[/bold yellow]")
    
    enhanced_context_prompt = f"""Generate a SPARQL query to answer: "How many buildings are there?"

ONTOLOGY CONTEXT:
- Building class relationships: {list(ontology_context.class_relationships.get('Building', []))[:3]}
- Property constraints: {list(ontology_context.property_constraints.keys())[:5]}

GUIDELINES CONTEXT:
- Property types available: {list(guidelines_context.property_types.keys())[:5]}
- Property definitions: {len(guidelines_context.property_definitions)} total

TTL FILE ANALYSIS:
{basic_analysis[:500]}...

Please generate a SPARQL query that:
1. Uses the exact prefixes and namespaces from the TTL analysis
2. Respects ontology domain/range constraints for property usage
3. Follows guidelines for property data types and validation rules
4. Targets the appropriate classes and properties for this question
5. Is syntactically correct and executable
"""

    try:
        result = await enhanced_sparql_agent.run(enhanced_context_prompt, deps=context)
        
        console.print(Panel(
            f"[bold green]Query Generated:[/bold green]\n{result.output.query}\n\n"
            f"[bold cyan]Description:[/bold cyan] {result.output.description}\n\n"
            f"[bold magenta]Ontology Compliance:[/bold magenta] {result.output.ontology_compliance}\n\n"
            f"[bold yellow]Guidelines Compliance:[/bold yellow] {result.output.guidelines_compliance}",
            title="🚀 Enhanced SPARQL Query",
            border_style="green"
        ))
        
        # Test the query
        console.print("\n[bold yellow]Testing Query Execution...[/bold yellow]")
        
        try:
            query_results = list(context.graph.query(result.output.query))
            console.print(f"✅ Query executed successfully: {len(query_results)} results")
            
            if query_results:
                console.print(f"Result: {query_results[0]}")
            
        except Exception as e:
            console.print(f"❌ Query execution failed: {e}")
        
    except Exception as e:
        console.print(f"❌ Enhanced SPARQL generation failed: {e}")
    
    console.print(Panel(
        "[bold green]✅ Simple test completed![/bold green]",
        border_style="green"
    ))

def main():
    """Main entry point"""
    try:
        asyncio.run(simple_test())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted!")
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    main()
