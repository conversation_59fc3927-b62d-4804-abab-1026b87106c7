#!/usr/bin/env python3
"""
Comprehensive Test Script for Enhanced TTL QA System with Dynamic Context Manager
Tests various question types and demonstrates context-aware capabilities
"""

import asyncio
import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.markdown import Markdown
from rich import box

# Import our enhanced system
from dynamic_context_manager import (
    EnhancedTTLContext, 
    analyze_ttl_file_basic,
    analyze_ontology,
    analyze_guidelines,
    enhanced_ttl_qa_agent
)
from rdflib import Graph

class EnhancedSystemTester:
    """Comprehensive tester for the enhanced TTL QA system"""
    
    def __init__(self):
        self.console = Console()
        self.test_questions = [
            {
                "category": "Basic Counting",
                "question": "How many buildings are there?",
                "expected_context": "Should use ontology to identify Building class and count instances"
            },
            {
                "category": "Property-based Filtering", 
                "question": "What buildings have parking spaces?",
                "expected_context": "Should use guidelines to validate parking-spaces property and its data type"
            },
            {
                "category": "Location-based Query",
                "question": "List all buildings in Germany",
                "expected_context": "Should use Address-Building relationship from ontology and country property from guidelines"
            },
            {
                "category": "Complex Relationship",
                "question": "Which buildings have addresses with postal codes?",
                "expected_context": "Should leverage ontology Address-Building relationship and guidelines postal-code validation"
            },
            {
                "category": "Data Type Validation",
                "question": "Show buildings with construction year after 2000",
                "expected_context": "Should use guidelines to validate construction-year as integer type"
            },
            {
                "category": "Multi-entity Analysis",
                "question": "Compare office buildings vs retail buildings",
                "expected_context": "Should use ontology relationships and guidelines property definitions for building types"
            },
            {
                "category": "Constraint-based Query",
                "question": "What are the energy efficiency classes of buildings?",
                "expected_context": "Should use guidelines validation rules for energy-efficiency-class property"
            },
            {
                "category": "Geographic Distribution",
                "question": "How many buildings are in each country?",
                "expected_context": "Should combine ontology Address-Building relationship with guidelines country property"
            }
        ]
    
    def print_header(self):
        """Print test header"""
        header_text = """
🧪 Enhanced TTL QA System - Comprehensive Test Suite
Dynamic Context Manager with Ontology & Guidelines Intelligence
        """
        
        header_panel = Panel(
            header_text.strip(),
            border_style="bright_blue",
            box=box.DOUBLE_EDGE,
            padding=(1, 2)
        )
        
        self.console.print(header_panel)
        self.console.print()
    
    def load_test_context(self) -> EnhancedTTLContext:
        """Load the test TTL file with ontology and guidelines"""
        self.console.print("[bold cyan]Loading Test Context...[/bold cyan]")
        
        # Define file paths
        ttl_path = "/home/<USER>/dev/BA/.phases/phase7_dynamic_context_manager/assets/example.ttl"
        ontology_path = "/home/<USER>/dev/BA/.phases/phase7_dynamic_context_manager/assets/ontology.ttl"
        guidelines_path = "/home/<USER>/dev/BA/.phases/phase7_dynamic_context_manager/assets/guideline.json"
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("Loading files...", total=None)
                
                # Load TTL graph
                progress.update(task, description="Loading TTL graph...")
                graph = Graph()
                graph.parse(ttl_path, format='turtle')
                
                # Analyze ontology
                progress.update(task, description="Analyzing ontology...")
                ontology_context = analyze_ontology(ontology_path)
                
                # Analyze guidelines
                progress.update(task, description="Processing guidelines...")
                guidelines_context = analyze_guidelines(guidelines_path)
                
                # Basic analysis
                progress.update(task, description="Building basic analysis...")
                basic_analysis = analyze_ttl_file_basic(ttl_path)
                
                progress.update(task, description="✅ Context loaded successfully!")
            
            context = EnhancedTTLContext(
                graph=graph,
                file_path=ttl_path,
                basic_analysis=basic_analysis,
                ontology_context=ontology_context,
                guidelines_context=guidelines_context
            )
            
            # Display context summary
            summary_table = Table(show_header=True, header_style="bold magenta")
            summary_table.add_column("Component", style="cyan")
            summary_table.add_column("Count", style="green")
            summary_table.add_column("Details", style="white")
            
            summary_table.add_row(
                "TTL Triples", 
                str(len(graph)), 
                "RDF statements in the knowledge graph"
            )
            summary_table.add_row(
                "Ontology Classes", 
                str(len(ontology_context.class_relationships)), 
                "Classes with defined relationships"
            )
            summary_table.add_row(
                "Ontology Properties", 
                str(len(ontology_context.property_constraints)), 
                "Properties with domain/range constraints"
            )
            summary_table.add_row(
                "Guidelines Properties", 
                str(len(guidelines_context.property_definitions)), 
                "Properties with validation rules and types"
            )
            
            self.console.print(Panel(
                summary_table,
                title="📊 Loaded Context Summary",
                border_style="green"
            ))
            
            return context
            
        except Exception as e:
            self.console.print(f"[red]❌ Error loading test context: {e}[/red]")
            sys.exit(1)
    
    async def test_question(self, question_info: dict, context: EnhancedTTLContext, question_num: int, total_questions: int):
        """Test a single question and display detailed results"""
        
        question = question_info["question"]
        category = question_info["category"]
        expected_context = question_info["expected_context"]
        
        self.console.print(f"\n[bold yellow]Test {question_num}/{total_questions}: {category}[/bold yellow]")
        
        # Display question and expectations
        question_panel = Panel(
            f"[bold cyan]Question:[/bold cyan] {question}\n\n"
            f"[bold magenta]Expected Context Usage:[/bold magenta] {expected_context}",
            title=f"🔍 Test Case: {category}",
            border_style="blue"
        )
        self.console.print(question_panel)
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console,
                transient=True
            ) as progress:
                task = progress.add_task(f"Processing question {question_num}...", total=None)
                
                # Run the enhanced agent
                result = await enhanced_ttl_qa_agent.run(
                    f"Please answer this question about the TTL data using full contextual analysis: {question}",
                    deps=context
                )
                
                progress.update(task, description="✅ Question processed!", completed=True)
            
            # Display the response
            response_panel = Panel(
                result.output,
                title="🤖 Enhanced Agent Response",
                border_style="green",
                padding=(1, 1)
            )
            self.console.print(response_panel)
            
            # Show usage stats
            usage = result.usage()
            if usage:
                usage_info = f"**Tokens:** {usage.total_tokens} | **Requests:** {usage.requests}"
                self.console.print(Panel(
                    Markdown(usage_info),
                    title="📈 Usage Stats",
                    border_style="dim"
                ))
            
            # Add separator
            self.console.print("─" * 80)
            
        except Exception as e:
            self.console.print(f"[red]❌ Error testing question: {e}[/red]")
    
    async def run_comprehensive_test(self):
        """Run the comprehensive test suite"""
        self.print_header()
        
        # Load test context
        context = self.load_test_context()
        
        self.console.print(f"\n[bold green]🚀 Starting Comprehensive Test Suite[/bold green]")
        self.console.print(f"[cyan]Testing {len(self.test_questions)} different question types...[/cyan]\n")
        
        # Test each question
        for i, question_info in enumerate(self.test_questions, 1):
            await self.test_question(question_info, context, i, len(self.test_questions))
            
            # Brief pause between questions for readability
            await asyncio.sleep(1)
        
        # Final summary
        self.console.print(Panel(
            f"[bold green]✅ Comprehensive Test Suite Completed![/bold green]\n\n"
            f"Tested {len(self.test_questions)} different question types demonstrating:\n"
            f"• Dynamic ontology context extraction\n"
            f"• Guidelines-based property validation\n"
            f"• Context-aware SPARQL query generation\n"
            f"• Enhanced result analysis with constraints\n"
            f"• Multi-entity relationship reasoning",
            title="🎉 Test Suite Summary",
            border_style="bright_green",
            padding=(1, 2)
        ))

def main():
    """Main entry point for the test suite"""
    tester = EnhancedSystemTester()
    try:
        asyncio.run(tester.run_comprehensive_test())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted!")
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    main()
