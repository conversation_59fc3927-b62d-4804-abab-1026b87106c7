#!/usr/bin/env python3
"""
Demo of Smart Dynamic Context Manager
Shows how the system selectively loads context based on question type
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

from dynamic_context_manager import (
    EnhancedTTLContext, 
    analyze_ontology, analyze_guidelines, analyze_ttl_file_basic,
    enhanced_ttl_qa_agent
)
from rdflib import Graph

# Rich imports for beautiful output
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich import box

console = Console()

async def demo_smart_context():
    """Demonstrate the smart context manager with different question types"""
    
    console.print(Panel(
        "[bold cyan]Smart Dynamic Context Manager Demo[/bold cyan]\n"
        "[dim]Showing selective context loading based on question analysis[/dim]",
        border_style="blue"
    ))
    
    # Set up test context
    console.print("\n[cyan]Setting up enhanced TTL context...[/cyan]")
    
    ttl_path = "/home/<USER>/dev/BA/.phases/phase7_dynamic_context_manager/assets/example.ttl"
    ontology_path = "/home/<USER>/dev/BA/.phases/phase7_dynamic_context_manager/assets/ontology.ttl"
    guidelines_path = "/home/<USER>/dev/BA/.phases/phase7_dynamic_context_manager/assets/guideline.json"
    
    # Check if files exist
    for path, name in [(ttl_path, "TTL"), (ontology_path, "Ontology"), (guidelines_path, "Guidelines")]:
        if not Path(path).exists():
            console.print(f"[red]❌ {name} file not found: {path}[/red]")
            return
    
    try:
        # Load TTL graph
        graph = Graph()
        graph.parse(ttl_path, format='turtle')
        
        # Analyze contexts
        ontology_context = analyze_ontology(ontology_path)
        guidelines_context = analyze_guidelines(guidelines_path)
        basic_analysis = analyze_ttl_file_basic(ttl_path)
        
        test_context = EnhancedTTLContext(
            graph=graph,
            file_path=ttl_path,
            basic_analysis=basic_analysis,
            ontology_context=ontology_context,
            guidelines_context=guidelines_context
        )
        
        console.print(f"[green]✅ Context loaded: {len(graph)} triples, {len(ontology_context.class_relationships)} class relationships, {len(guidelines_context.property_definitions)} property definitions[/green]")
        
    except Exception as e:
        console.print(f"[red]❌ Error setting up context: {e}[/red]")
        return
    
    # Demo questions of different types
    demo_questions = [
        {
            "question": "How many triples are in the dataset?",
            "description": "Simple counting question - should use only basic analysis",
            "expected_efficiency": "High - minimal context loading"
        },
        {
            "question": "What are the class relationships in the ontology?",
            "description": "Ontology structure question - should load ontology context",
            "expected_efficiency": "Medium - ontology context only"
        },
        {
            "question": "What validation rules apply to numeric properties?",
            "description": "Guidelines question - should load guidelines context",
            "expected_efficiency": "Medium - guidelines context only"
        }
    ]
    
    console.print("\n[bold]Running Smart Context Analysis Demo:[/bold]")
    
    for i, demo in enumerate(demo_questions, 1):
        console.print(f"\n[cyan]Demo {i}: {demo['description']}[/cyan]")
        console.print(f"[dim]Question: {demo['question']}[/dim]")
        console.print(f"[dim]Expected: {demo['expected_efficiency']}[/dim]")
        
        try:
            # Run the enhanced agent with smart context management
            console.print("[yellow]🤖 Running smart agent...[/yellow]")
            
            result = await enhanced_ttl_qa_agent.run(
                demo['question'],
                deps=test_context
            )
            
            # Display the response
            console.print(Panel(
                result.output,
                title=f"🚀 Smart Response {i}",
                border_style="green",
                padding=(1, 2)
            ))
            
            # Show usage stats
            usage = result.usage()
            if usage:
                console.print(f"[dim]Usage: {usage.requests} requests, {usage.total_tokens} tokens[/dim]")
            
        except Exception as e:
            console.print(f"[red]❌ Error running demo {i}: {e}[/red]")
    
    # Summary
    console.print(Panel(
        "[bold green]🎉 Smart Context Manager Demo Complete![/bold green]\n\n"
        "[bold]Key Benefits Demonstrated:[/bold]\n"
        "• ✅ Selective context loading based on question analysis\n"
        "• ✅ Reduced unnecessary tool calls for simple questions\n"
        "• ✅ Smart detection of when ontology/guidelines are needed\n"
        "• ✅ Improved efficiency while maintaining accuracy\n\n"
        "[bold]The system now intelligently decides what context to load,[/bold]\n"
        "[bold]making it truly dynamic and efficient![/bold]",
        title="📊 Demo Summary",
        border_style="green"
    ))

async def main():
    """Main demo function"""
    await demo_smart_context()

if __name__ == "__main__":
    asyncio.run(main())
