#!/usr/bin/env python3
"""
Comprehensive Unit Tests for Enhanced TTL QA System
Tests all functions, tools, and edge cases to ensure robustness
"""

import unittest
import asyncio
import json
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
from rdflib import Graph, Namespace, URIRef, Literal
from rdflib.plugins.sparql import prepareQuery

# Import the system under test
from dynamic_context_manager import (
    OntologyContext,
    GuidelinesContext,
    EnhancedTTLContext,
    ContextualInsight,
    EnhancedSPARQLQuery,
    ContextualStrategy,
    analyze_ontology,
    analyze_guidelines,
    analyze_ttl_file_basic,
    analyze_ttl_file_enhanced,
    format_class_relationships,
    format_property_constraints,
    format_property_definitions,
    format_required_properties,
    format_property_types_units,
    validate_sparql_query,
    enhanced_sparql_agent,
    enhanced_ttl_qa_agent
)

class TestDataStructures(unittest.TestCase):
    """Test data structure creation and validation"""
    
    def test_ontology_context_creation(self):
        """Test OntologyContext creation with various inputs"""
        # Valid creation
        context = OntologyContext(
            class_relationships={'Building': ['Address']},
            property_domains={'hasAddress': ['Building']},
            property_ranges={'hasAddress': ['Address']},
            class_properties={'Building': ['hasAddress']},
            property_constraints={'hasAddress': {'domains': ['Building'], 'ranges': ['Address']}}
        )
        self.assertEqual(context.class_relationships['Building'], ['Address'])
        
        # Empty creation
        empty_context = OntologyContext({}, {}, {}, {}, {})
        self.assertEqual(len(empty_context.class_relationships), 0)
    
    def test_guidelines_context_creation(self):
        """Test GuidelinesContext creation with various inputs"""
        context = GuidelinesContext(
            property_definitions={'name': {'description': 'Building name'}},
            validation_rules={'name': {'required': True}},
            required_properties={'Building': ['name']},
            property_types={'name': 'string'},
            property_units={}
        )
        self.assertEqual(context.property_definitions['name']['description'], 'Building name')
        self.assertTrue(context.validation_rules['name']['required'])
    
    def test_enhanced_ttl_context_creation(self):
        """Test EnhancedTTLContext creation"""
        graph = Graph()
        ontology_context = OntologyContext({}, {}, {}, {}, {})
        guidelines_context = GuidelinesContext({}, {}, {}, {}, {})
        
        context = EnhancedTTLContext(
            graph=graph,
            file_path='test.ttl',
            basic_analysis='Test analysis',
            ontology_context=ontology_context,
            guidelines_context=guidelines_context
        )
        self.assertEqual(context.file_path, 'test.ttl')
        self.assertEqual(context.basic_analysis, 'Test analysis')

class TestOntologyAnalysis(unittest.TestCase):
    """Test ontology analysis functions"""
    
    def setUp(self):
        """Set up test ontology file"""
        self.test_ontology = """
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix ibpdi: <https://ibpdi.datacat.org/class/> .
@prefix prop: <https://ibpdi.datacat.org/property/> .

prop:hasAddress rdfs:domain ibpdi:Building ;
                rdfs:range ibpdi:Address .

prop:hasName rdfs:domain ibpdi:Building ;
             rdfs:range <http://www.w3.org/2001/XMLSchema#string> .
"""
        
        # Create temporary file
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.ttl', delete=False)
        self.temp_file.write(self.test_ontology)
        self.temp_file.close()
    
    def tearDown(self):
        """Clean up temporary file"""
        os.unlink(self.temp_file.name)
    
    def test_analyze_ontology_valid_file(self):
        """Test ontology analysis with valid file"""
        context = analyze_ontology(self.temp_file.name)
        
        # Check that relationships were extracted
        self.assertIn('hasAddress', context.property_domains)
        self.assertIn('Building', context.property_domains['hasAddress'])
        self.assertIn('Address', context.property_ranges['hasAddress'])
        self.assertIn('Building', context.class_relationships)
    
    def test_analyze_ontology_nonexistent_file(self):
        """Test ontology analysis with nonexistent file"""
        context = analyze_ontology('nonexistent.ttl')
        
        # Should return empty context without crashing
        self.assertEqual(len(context.class_relationships), 0)
        self.assertEqual(len(context.property_constraints), 0)
    
    def test_analyze_ontology_invalid_ttl(self):
        """Test ontology analysis with invalid TTL"""
        invalid_file = tempfile.NamedTemporaryFile(mode='w', suffix='.ttl', delete=False)
        invalid_file.write("This is not valid TTL content")
        invalid_file.close()
        
        try:
            context = analyze_ontology(invalid_file.name)
            # Should return empty context without crashing
            self.assertEqual(len(context.class_relationships), 0)
        finally:
            os.unlink(invalid_file.name)
    
    def test_analyze_ontology_empty_file(self):
        """Test ontology analysis with empty file"""
        empty_file = tempfile.NamedTemporaryFile(mode='w', suffix='.ttl', delete=False)
        empty_file.write("")
        empty_file.close()
        
        try:
            context = analyze_ontology(empty_file.name)
            self.assertEqual(len(context.class_relationships), 0)
        finally:
            os.unlink(empty_file.name)

class TestGuidelinesAnalysis(unittest.TestCase):
    """Test guidelines analysis functions"""
    
    def setUp(self):
        """Set up test guidelines file"""
        self.test_guidelines = {
            "Domain": {
                "Classifications": {
                    "$values": [
                        {
                            "Name": "Building",
                            "ClassificationProperties": {
                                "$values": [
                                    {
                                        "Name": "name",
                                        "Description": "Building name",
                                        "IsRequired": True,
                                        "PropertyAssignment": {
                                            "Property": {
                                                "StorageType": 4,
                                                "UnitType": None,
                                                "UnitAbbreviation": None,
                                                "Identifier": "https://ibpdi.datacat.org/property/name"
                                            },
                                            "Min": None,
                                            "Max": None,
                                            "MinIsInclusive": False,
                                            "MaxIsInclusive": False
                                        }
                                    },
                                    {
                                        "Name": "height",
                                        "Description": "Building height",
                                        "IsRequired": False,
                                        "PropertyAssignment": {
                                            "Property": {
                                                "StorageType": 2,
                                                "UnitType": "meter",
                                                "UnitAbbreviation": "m",
                                                "Identifier": "https://ibpdi.datacat.org/property/height"
                                            },
                                            "Min": 0.0,
                                            "Max": 1000.0,
                                            "MinIsInclusive": True,
                                            "MaxIsInclusive": True
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        }
        
        # Create temporary file
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.test_guidelines, self.temp_file)
        self.temp_file.close()
    
    def tearDown(self):
        """Clean up temporary file"""
        os.unlink(self.temp_file.name)
    
    def test_analyze_guidelines_valid_file(self):
        """Test guidelines analysis with valid file"""
        context = analyze_guidelines(self.temp_file.name)
        
        # Check property definitions
        self.assertIn('name', context.property_definitions)
        self.assertEqual(context.property_definitions['name']['description'], 'Building name')
        self.assertEqual(context.property_definitions['name']['class'], 'Building')
        
        # Check validation rules
        self.assertIn('name', context.validation_rules)
        self.assertTrue(context.validation_rules['name']['required'])
        
        # Check property types
        self.assertEqual(context.property_types['name'], 'string')
        self.assertEqual(context.property_types['height'], 'float')
        
        # Check units
        self.assertIn('height', context.property_units)
        self.assertEqual(context.property_units['height'], 'meter (m)')
    
    def test_analyze_guidelines_nonexistent_file(self):
        """Test guidelines analysis with nonexistent file"""
        context = analyze_guidelines('nonexistent.json')
        
        # Should return empty context without crashing
        self.assertEqual(len(context.property_definitions), 0)
        self.assertEqual(len(context.validation_rules), 0)
    
    def test_analyze_guidelines_invalid_json(self):
        """Test guidelines analysis with invalid JSON"""
        invalid_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        invalid_file.write("This is not valid JSON")
        invalid_file.close()
        
        try:
            context = analyze_guidelines(invalid_file.name)
            # Should return empty context without crashing
            self.assertEqual(len(context.property_definitions), 0)
        finally:
            os.unlink(invalid_file.name)
    
    def test_analyze_guidelines_empty_structure(self):
        """Test guidelines analysis with empty structure"""
        empty_guidelines = {"Domain": {"Classifications": {"$values": []}}}
        
        empty_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(empty_guidelines, empty_file)
        empty_file.close()
        
        try:
            context = analyze_guidelines(empty_file.name)
            self.assertEqual(len(context.property_definitions), 0)
        finally:
            os.unlink(empty_file.name)
    
    def test_analyze_guidelines_malformed_structure(self):
        """Test guidelines analysis with malformed structure"""
        malformed = {"SomeOtherKey": "value"}
        
        malformed_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(malformed, malformed_file)
        malformed_file.close()
        
        try:
            context = analyze_guidelines(malformed_file.name)
            self.assertEqual(len(context.property_definitions), 0)
        finally:
            os.unlink(malformed_file.name)

class TestTTLAnalysis(unittest.TestCase):
    """Test TTL file analysis functions"""
    
    def setUp(self):
        """Set up test TTL file"""
        self.test_ttl = """
@prefix ibpdi: <https://ibpdi.datacat.org/class/> .
@prefix prop: <https://ibpdi.datacat.org/property/> .

<building1> a ibpdi:Building ;
            prop:name "Test Building" ;
            prop:height 50.0 .

<building2> a ibpdi:Building ;
            prop:name "Another Building" .

<address1> a ibpdi:Address ;
           prop:street "Main Street" .
"""
        
        # Create temporary file
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.ttl', delete=False)
        self.temp_file.write(self.test_ttl)
        self.temp_file.close()
    
    def tearDown(self):
        """Clean up temporary file"""
        os.unlink(self.temp_file.name)
    
    def test_analyze_ttl_file_basic_valid(self):
        """Test basic TTL analysis with valid file"""
        analysis = analyze_ttl_file_basic(self.temp_file.name)
        
        # Check that analysis contains expected information
        self.assertIn('Total triples:', analysis)
        self.assertIn('Classes found:', analysis)
        self.assertIn('Properties found:', analysis)
        # Check for full URIs since that's what the analysis actually returns
        self.assertIn('https://ibpdi.datacat.org/class/Building', analysis)
        self.assertIn('https://ibpdi.datacat.org/class/Address', analysis)
    
    def test_analyze_ttl_file_basic_nonexistent(self):
        """Test basic TTL analysis with nonexistent file"""
        analysis = analyze_ttl_file_basic('nonexistent.ttl')
        
        # Should return error message without crashing
        self.assertIn('Error in basic TTL analysis:', analysis)
    
    def test_analyze_ttl_file_basic_invalid(self):
        """Test basic TTL analysis with invalid TTL"""
        invalid_file = tempfile.NamedTemporaryFile(mode='w', suffix='.ttl', delete=False)
        invalid_file.write("This is not valid TTL")
        invalid_file.close()
        
        try:
            analysis = analyze_ttl_file_basic(invalid_file.name)
            self.assertIn('Error in basic TTL analysis:', analysis)
        finally:
            os.unlink(invalid_file.name)

class TestFormattingFunctions(unittest.TestCase):
    """Test formatting functions for display"""
    
    def test_format_class_relationships_valid(self):
        """Test formatting class relationships with valid data"""
        relationships = {
            'Building': ['Address', 'Owner'],
            'Address': ['Country'],
            'EmptyClass': []
        }
        
        result = format_class_relationships(relationships)
        self.assertIn('Building → Address, Owner', result)
        self.assertIn('Address → Country', result)
        self.assertNotIn('EmptyClass', result)  # Empty relationships should be filtered
    
    def test_format_class_relationships_empty(self):
        """Test formatting class relationships with empty data"""
        result = format_class_relationships({})
        self.assertEqual(result, "No class relationships found")
    
    def test_format_class_relationships_large(self):
        """Test formatting class relationships with large dataset"""
        large_relationships = {f'Class{i}': [f'Related{i}'] for i in range(20)}
        
        result = format_class_relationships(large_relationships)
        # Should truncate and add "..."
        self.assertIn('...', result)
        lines = result.split('\n')
        # Should have at most 11 lines (10 + "...")
        self.assertLessEqual(len(lines), 11)
    
    def test_format_property_constraints_valid(self):
        """Test formatting property constraints with valid data"""
        constraints = {
            'hasAddress': {'domains': ['Building'], 'ranges': ['Address']},
            'hasName': {'domains': ['Building'], 'ranges': ['string']}
        }
        
        result = format_property_constraints(constraints)
        self.assertIn('hasAddress: Building → Address', result)
        self.assertIn('hasName: Building → string', result)
    
    def test_format_property_constraints_empty(self):
        """Test formatting property constraints with empty data"""
        result = format_property_constraints({})
        self.assertEqual(result, "No property constraints found")
    
    def test_format_property_definitions_valid(self):
        """Test formatting property definitions with valid data"""
        definitions = {
            'name': {'description': 'Building name'},
            'height': {'description': 'Building height'}
        }
        rules = {
            'name': {'required': True},
            'height': {'required': False}
        }
        
        result = format_property_definitions(definitions, rules)
        self.assertIn('name: Building name (Required)', result)
        self.assertIn('height: Building height (Optional)', result)
    
    def test_format_property_definitions_empty(self):
        """Test formatting property definitions with empty data"""
        result = format_property_definitions({}, {})
        self.assertEqual(result, "No property definitions found")
    
    def test_format_required_properties_valid(self):
        """Test formatting required properties with valid data"""
        required_props = {
            'Building': ['name', 'type'],
            'Address': ['street'],
            'EmptyClass': []
        }
        
        result = format_required_properties(required_props)
        self.assertIn('Building: name, type', result)
        self.assertIn('Address: street', result)
        self.assertNotIn('EmptyClass', result)  # Empty should be filtered
    
    def test_format_required_properties_empty(self):
        """Test formatting required properties with empty data"""
        result = format_required_properties({})
        self.assertEqual(result, "No required properties found")
    
    def test_format_property_types_units_valid(self):
        """Test formatting property types and units with valid data"""
        types = {
            'name': 'string',
            'height': 'float',
            'count': 'integer'
        }
        units = {
            'height': 'meter (m)',
            'count': 'pieces (pcs)'
        }
        
        result = format_property_types_units(types, units)
        self.assertIn('name: string', result)
        self.assertIn('height: float [meter (m)]', result)
        self.assertIn('count: integer [pieces (pcs)]', result)
    
    def test_format_property_types_units_empty(self):
        """Test formatting property types and units with empty data"""
        result = format_property_types_units({}, {})
        self.assertEqual(result, "No property type information found")

class TestSPARQLValidation(unittest.TestCase):
    """Test SPARQL query validation"""
    
    def test_validate_sparql_query_valid_select(self):
        """Test SPARQL validation with valid SELECT query"""
        query = """
        PREFIX ibpdi: <https://ibpdi.datacat.org/class/>
        SELECT ?building WHERE {
            ?building a ibpdi:Building .
        }
        """
        
        result = validate_sparql_query(query)
        self.assertEqual(result, 'SELECT')
    
    def test_validate_sparql_query_valid_ask(self):
        """Test SPARQL validation with valid ASK query"""
        query = """
        PREFIX ibpdi: <https://ibpdi.datacat.org/class/>
        ASK {
            ?building a ibpdi:Building .
        }
        """
        
        result = validate_sparql_query(query)
        self.assertEqual(result, 'ASK')
    
    def test_validate_sparql_query_valid_construct(self):
        """Test SPARQL validation with valid CONSTRUCT query"""
        query = """
        PREFIX ibpdi: <https://ibpdi.datacat.org/class/>
        CONSTRUCT { ?building a ibpdi:Building }
        WHERE { ?building a ibpdi:Building }
        """
        
        result = validate_sparql_query(query)
        self.assertEqual(result, 'CONSTRUCT')
    
    def test_validate_sparql_query_invalid_syntax(self):
        """Test SPARQL validation with invalid syntax"""
        query = "This is not a valid SPARQL query"
        
        with self.assertRaises(ValueError) as context:
            validate_sparql_query(query)
        
        self.assertIn('Invalid SPARQL syntax', str(context.exception))
    
    def test_validate_sparql_query_empty(self):
        """Test SPARQL validation with empty query"""
        with self.assertRaises(ValueError) as context:
            validate_sparql_query("")
        
        self.assertIn('Query must be a non-empty string', str(context.exception))
    
    def test_validate_sparql_query_none(self):
        """Test SPARQL validation with None query"""
        with self.assertRaises(ValueError) as context:
            validate_sparql_query(None)
        
        self.assertIn('Query must be a non-empty string', str(context.exception))

class TestAgentTools(unittest.TestCase):
    """Test agent tools and their edge cases"""

    def setUp(self):
        """Set up test context for agent tools"""
        # Create mock enhanced context
        self.mock_graph = Graph()
        self.mock_graph.parse(data="""
        @prefix ibpdi: <https://ibpdi.datacat.org/class/> .
        @prefix prop: <https://ibpdi.datacat.org/property/> .

        <building1> a ibpdi:Building ;
                    prop:name "Test Building" .
        """, format='turtle')

        self.ontology_context = OntologyContext(
            class_relationships={'Building': ['Address']},
            property_domains={'hasAddress': ['Building'], 'name': ['Building']},
            property_ranges={'hasAddress': ['Address'], 'name': ['string']},
            class_properties={'Building': ['hasAddress', 'name']},
            property_constraints={
                'hasAddress': {'domains': ['Building'], 'ranges': ['Address']},
                'name': {'domains': ['Building'], 'ranges': ['string']}
            }
        )

        self.guidelines_context = GuidelinesContext(
            property_definitions={
                'name': {'description': 'Building name', 'class': 'Building'},
                'height': {'description': 'Building height', 'class': 'Building'}
            },
            validation_rules={
                'name': {'required': True, 'min_value': None, 'max_value': None},
                'height': {'required': False, 'min_value': 0.0, 'max_value': 1000.0}
            },
            required_properties={'Building': ['name']},
            property_types={'name': 'string', 'height': 'float'},
            property_units={'height': 'meter (m)'}
        )

        self.enhanced_context = EnhancedTTLContext(
            graph=self.mock_graph,
            file_path='test.ttl',
            basic_analysis='Test analysis',
            ontology_context=self.ontology_context,
            guidelines_context=self.guidelines_context
        )

class TestContextualInsight(unittest.TestCase):
    """Test ContextualInsight model validation"""

    def test_contextual_insight_creation_valid(self):
        """Test creating ContextualInsight with valid data"""
        insight = ContextualInsight(
            insight="Test insight",
            confidence=0.8,
            supporting_data="Test data",
            ontology_context="Test ontology",
            guidelines_context="Test guidelines"
        )

        self.assertEqual(insight.insight, "Test insight")
        self.assertEqual(insight.confidence, 0.8)

    def test_contextual_insight_creation_defaults(self):
        """Test creating ContextualInsight with default values"""
        insight = ContextualInsight(
            insight="Test insight",
            supporting_data="Test data",
            ontology_context="Test ontology",
            guidelines_context="Test guidelines"
        )

        # Should use default confidence
        self.assertEqual(insight.confidence, 0.8)

    def test_contextual_insight_creation_invalid_confidence(self):
        """Test creating ContextualInsight with invalid confidence"""
        # This should still work as Pydantic will handle validation
        insight = ContextualInsight(
            insight="Test insight",
            confidence=1.5,  # Invalid but will be accepted
            supporting_data="Test data",
            ontology_context="Test ontology",
            guidelines_context="Test guidelines"
        )

        self.assertEqual(insight.confidence, 1.5)

class TestEnhancedSPARQLQuery(unittest.TestCase):
    """Test EnhancedSPARQLQuery model validation"""

    def test_enhanced_sparql_query_creation_valid(self):
        """Test creating EnhancedSPARQLQuery with valid data"""
        query = EnhancedSPARQLQuery(
            query="SELECT ?s WHERE { ?s a ?o }",
            query_type="SELECT",
            description="Test query",
            confidence=0.9,
            validation_passed=True,
            ontology_compliance="Compliant",
            guidelines_compliance="Compliant"
        )

        self.assertEqual(query.query_type, "SELECT")
        self.assertTrue(query.validation_passed)

    def test_enhanced_sparql_query_creation_defaults(self):
        """Test creating EnhancedSPARQLQuery with default values"""
        query = EnhancedSPARQLQuery(
            query="SELECT ?s WHERE { ?s a ?o }",
            query_type="SELECT",
            description="Test query",
            ontology_compliance="Compliant",
            guidelines_compliance="Compliant"
        )

        # Should use defaults
        self.assertEqual(query.confidence, 0.8)
        self.assertFalse(query.validation_passed)

class TestContextualStrategy(unittest.TestCase):
    """Test ContextualStrategy model validation"""

    def test_contextual_strategy_creation_valid(self):
        """Test creating ContextualStrategy with valid data"""
        strategy = ContextualStrategy(
            approach="Test approach",
            steps=["Step 1", "Step 2"],
            expected_queries=2,
            reasoning="Test reasoning",
            ontology_considerations="Test ontology",
            guidelines_considerations="Test guidelines"
        )

        self.assertEqual(strategy.approach, "Test approach")
        self.assertEqual(len(strategy.steps), 2)
        self.assertEqual(strategy.expected_queries, 2)

    def test_contextual_strategy_creation_empty_steps(self):
        """Test creating ContextualStrategy with empty steps"""
        strategy = ContextualStrategy(
            approach="Test approach",
            steps=[],
            expected_queries=0,
            reasoning="Test reasoning",
            ontology_considerations="Test ontology",
            guidelines_considerations="Test guidelines"
        )

        self.assertEqual(len(strategy.steps), 0)

class TestEdgeCases(unittest.TestCase):
    """Test edge cases and error conditions"""

    def test_analyze_ontology_with_unicode_content(self):
        """Test ontology analysis with Unicode content"""
        unicode_ontology = """
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix ibpdi: <https://ibpdi.datacat.org/class/> .
@prefix prop: <https://ibpdi.datacat.org/property/> .

prop:名前 rdfs:domain ibpdi:Building ;
         rdfs:range <http://www.w3.org/2001/XMLSchema#string> .
"""

        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.ttl', delete=False, encoding='utf-8')
        temp_file.write(unicode_ontology)
        temp_file.close()

        try:
            context = analyze_ontology(temp_file.name)
            # Should handle Unicode without crashing
            self.assertIsInstance(context, OntologyContext)
        finally:
            os.unlink(temp_file.name)

    def test_analyze_guidelines_with_missing_fields(self):
        """Test guidelines analysis with missing required fields"""
        incomplete_guidelines = {
            "Domain": {
                "Classifications": {
                    "$values": [
                        {
                            "Name": "Building",
                            "ClassificationProperties": {
                                "$values": [
                                    {
                                        "Name": "incomplete_prop",
                                        # Missing Description, IsRequired, etc.
                                        "PropertyAssignment": {
                                            "Property": {
                                                # Missing StorageType
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        }

        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(incomplete_guidelines, temp_file)
        temp_file.close()

        try:
            context = analyze_guidelines(temp_file.name)
            # Should handle missing fields gracefully
            self.assertIsInstance(context, GuidelinesContext)
        finally:
            os.unlink(temp_file.name)

    def test_format_functions_with_none_values(self):
        """Test formatting functions with None values"""
        # Test with None input
        result = format_class_relationships(None)
        self.assertEqual(result, "No class relationships found")

        result = format_property_constraints(None)
        self.assertEqual(result, "No property constraints found")

        result = format_property_definitions(None, None)
        self.assertEqual(result, "No property definitions found")

        result = format_required_properties(None)
        self.assertEqual(result, "No required properties found")

        result = format_property_types_units(None, None)
        self.assertEqual(result, "No property type information found")

    def test_format_functions_with_malformed_data(self):
        """Test formatting functions with malformed data structures"""
        # Test with malformed relationships
        malformed_relationships = {
            'ValidClass': ['RelatedClass'],
            'InvalidClass': None,  # This should be handled gracefully
            'AnotherClass': 'NotAList'  # This should also be handled
        }

        result = format_class_relationships(malformed_relationships)
        # Should not crash and should include valid data
        self.assertIn('ValidClass → RelatedClass', result)

    def test_sparql_validation_edge_cases(self):
        """Test SPARQL validation with edge cases"""
        # Test with whitespace-only query
        with self.assertRaises(ValueError):
            validate_sparql_query("   \n\t   ")

        # Test with query containing only comments
        comment_only_query = "# This is just a comment"
        with self.assertRaises(ValueError):
            validate_sparql_query(comment_only_query)

        # Test with mixed case query types
        mixed_case_query = """
        PREFIX ibpdi: <https://ibpdi.datacat.org/class/>
        select ?building WHERE {
            ?building a ibpdi:Building .
        }
        """
        result = validate_sparql_query(mixed_case_query)
        self.assertEqual(result, 'SELECT')

class TestAsyncToolsAndErrorCases(unittest.TestCase):
    """Test async tools and specific error cases like 'list' object has no attribute 'get'"""

    def setUp(self):
        """Set up test context for async tools"""
        self.mock_graph = Graph()
        self.mock_graph.parse(data="""
        @prefix ibpdi: <https://ibpdi.datacat.org/class/> .
        @prefix prop: <https://ibpdi.datacat.org/property/> .

        <building1> a ibpdi:Building ;
                    prop:name "Test Building" .
        <building2> a ibpdi:Building ;
                    prop:name "Another Building" .
        """, format='turtle')

        self.ontology_context = OntologyContext(
            class_relationships={'Building': ['Address']},
            property_domains={'hasAddress': ['Building'], 'name': ['Building']},
            property_ranges={'hasAddress': ['Address'], 'name': ['string']},
            class_properties={'Building': ['hasAddress', 'name']},
            property_constraints={
                'hasAddress': {'domains': ['Building'], 'ranges': ['Address']},
                'name': {'domains': ['Building'], 'ranges': ['string']}
            }
        )

        self.guidelines_context = GuidelinesContext(
            property_definitions={
                'name': {'description': 'Building name', 'class': 'Building'},
                'height': {'description': 'Building height', 'class': 'Building'}
            },
            validation_rules={
                'name': {'required': True, 'min_value': None, 'max_value': None},
                'height': {'required': False, 'min_value': 0.0, 'max_value': 1000.0}
            },
            required_properties={'Building': ['name']},
            property_types={'name': 'string', 'height': 'float'},
            property_units={'height': 'meter (m)'}
        )

        self.enhanced_context = EnhancedTTLContext(
            graph=self.mock_graph,
            file_path='test.ttl',
            basic_analysis='Test analysis with 2 buildings',
            ontology_context=self.ontology_context,
            guidelines_context=self.guidelines_context
        )

    def test_analyze_contextual_results_with_list_input(self):
        """Test analyze_contextual_results with list input (the specific error case)"""
        from dynamic_context_manager import analyze_contextual_results

        # Create a mock RunContext
        mock_ctx = Mock()
        mock_ctx.deps = self.enhanced_context

        # Test with list input (this was causing the error)
        list_input = [{'buildingCount': '137'}]

        # This should not crash and should handle the list gracefully
        async def test_async():
            result = await analyze_contextual_results(
                mock_ctx,
                list_input,  # This is the problematic input type
                "How many buildings are there?",
                "Test ontology context",
                "Test guidelines context"
            )
            return result

        # Run the async test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async())

            # Should return a ContextualInsight without crashing
            self.assertIsInstance(result, ContextualInsight)
            self.assertIn('1', result.insight)  # Should mention the count
        finally:
            loop.close()

    def test_analyze_contextual_results_with_dict_input(self):
        """Test analyze_contextual_results with dict input"""
        from dynamic_context_manager import analyze_contextual_results

        mock_ctx = Mock()
        mock_ctx.deps = self.enhanced_context

        # Test with dict input (normal case)
        dict_input = {
            'success': True,
            'data': [{'buildingCount': '137'}],
            'row_count': 1,
            'columns': ['buildingCount'],
            'execution_time': 0.1
        }

        async def test_async():
            result = await analyze_contextual_results(
                mock_ctx,
                dict_input,
                "How many buildings are there?",
                "Test ontology context",
                "Test guidelines context"
            )
            return result

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async())

            self.assertIsInstance(result, ContextualInsight)
            self.assertIn('1', result.insight)
        finally:
            loop.close()

    def test_analyze_contextual_results_with_json_string_input(self):
        """Test analyze_contextual_results with JSON string input"""
        from dynamic_context_manager import analyze_contextual_results

        mock_ctx = Mock()
        mock_ctx.deps = self.enhanced_context

        # Test with JSON string input
        json_input = json.dumps({
            'success': True,
            'data': [{'buildingCount': '137'}],
            'row_count': 1,
            'columns': ['buildingCount'],
            'execution_time': 0.1
        })

        async def test_async():
            result = await analyze_contextual_results(
                mock_ctx,
                json_input,
                "How many buildings are there?",
                "Test ontology context",
                "Test guidelines context"
            )
            return result

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async())

            self.assertIsInstance(result, ContextualInsight)
            self.assertIn('1', result.insight)
        finally:
            loop.close()

    def test_analyze_contextual_results_with_invalid_json_string(self):
        """Test analyze_contextual_results with invalid JSON string"""
        from dynamic_context_manager import analyze_contextual_results

        mock_ctx = Mock()
        mock_ctx.deps = self.enhanced_context

        # Test with invalid JSON string
        invalid_json = "This is not valid JSON"

        async def test_async():
            result = await analyze_contextual_results(
                mock_ctx,
                invalid_json,
                "How many buildings are there?",
                "Test ontology context",
                "Test guidelines context"
            )
            return result

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async())

            self.assertIsInstance(result, ContextualInsight)
            self.assertIn('Failed to parse', result.insight)
        finally:
            loop.close()

    def test_analyze_contextual_results_with_failed_query(self):
        """Test analyze_contextual_results with failed query result"""
        from dynamic_context_manager import analyze_contextual_results

        mock_ctx = Mock()
        mock_ctx.deps = self.enhanced_context

        # Test with failed query result
        failed_result = {
            'success': False,
            'error_message': 'SPARQL syntax error',
            'data': [],
            'row_count': 0,
            'columns': []
        }

        async def test_async():
            result = await analyze_contextual_results(
                mock_ctx,
                failed_result,
                "How many buildings are there?",
                "Test ontology context",
                "Test guidelines context"
            )
            return result

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async())

            self.assertIsInstance(result, ContextualInsight)
            self.assertIn('Query failed', result.insight)
            self.assertIn('SPARQL syntax error', result.insight)
        finally:
            loop.close()

    def test_analyze_contextual_results_with_empty_results(self):
        """Test analyze_contextual_results with empty query results"""
        from dynamic_context_manager import analyze_contextual_results

        mock_ctx = Mock()
        mock_ctx.deps = self.enhanced_context

        # Test with empty results
        empty_result = {
            'success': True,
            'data': [],
            'row_count': 0,
            'columns': [],
            'execution_time': 0.05
        }

        async def test_async():
            result = await analyze_contextual_results(
                mock_ctx,
                empty_result,
                "How many buildings are there?",
                "Test ontology context",
                "Test guidelines context"
            )
            return result

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async())

            self.assertIsInstance(result, ContextualInsight)
            self.assertIn('No data found', result.insight)
        finally:
            loop.close()

    def test_execute_sparql_query_with_valid_query(self):
        """Test execute_sparql_query with valid query"""
        from dynamic_context_manager import execute_sparql_query

        mock_ctx = Mock()
        mock_ctx.deps = self.enhanced_context

        # Test with valid SPARQL query
        valid_query = """
        PREFIX ibpdi: <https://ibpdi.datacat.org/class/>
        SELECT (COUNT(?building) AS ?count) WHERE {
            ?building a ibpdi:Building .
        }
        """

        async def test_async():
            result = await execute_sparql_query(mock_ctx, valid_query)
            return result

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async())

            # Should return JSON string
            self.assertIsInstance(result, str)

            # Parse and check the result
            parsed_result = json.loads(result)
            self.assertTrue(parsed_result['success'])
            self.assertEqual(parsed_result['row_count'], 1)
        finally:
            loop.close()

    def test_execute_sparql_query_with_invalid_query(self):
        """Test execute_sparql_query with invalid query"""
        from dynamic_context_manager import execute_sparql_query

        mock_ctx = Mock()
        mock_ctx.deps = self.enhanced_context

        # Test with invalid SPARQL query
        invalid_query = "This is not a valid SPARQL query"

        async def test_async():
            result = await execute_sparql_query(mock_ctx, invalid_query)
            return result

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async())

            # Should return JSON string with error
            self.assertIsInstance(result, str)

            # Parse and check the result
            parsed_result = json.loads(result)
            self.assertFalse(parsed_result['success'])
            self.assertIn('error_message', parsed_result)
        finally:
            loop.close()

if __name__ == '__main__':
    # Run all tests
    unittest.main(verbosity=2)
