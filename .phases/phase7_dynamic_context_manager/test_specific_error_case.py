#!/usr/bin/env python3
"""
Test for the specific error case: 'list' object has no attribute 'get'
This reproduces and verifies the fix for the exact error mentioned by the user
"""

import asyncio
import unittest
from unittest.mock import Mock
from rdflib import Graph

from dynamic_context_manager import (
    EnhancedTTLContext,
    OntologyContext,
    GuidelinesContext,
    analyze_contextual_results,
    ContextualInsight
)

class TestSpecificErrorCase(unittest.TestCase):
    """Test the specific 'list' object has no attribute 'get' error"""
    
    def setUp(self):
        """Set up test context"""
        self.mock_graph = Graph()
        self.mock_graph.parse(data="""
        @prefix ibpdi: <https://ibpdi.datacat.org/class/> .
        @prefix prop: <https://ibpdi.datacat.org/property/> .
        
        <building1> a ibpdi:Building ;
                    prop:name "Test Building" .
        """, format='turtle')
        
        self.ontology_context = OntologyContext(
            class_relationships={'Building': ['Address']},
            property_domains={'hasAddress': ['Building']},
            property_ranges={'hasAddress': ['Address']},
            class_properties={'Building': ['hasAddress']},
            property_constraints={'hasAddress': {'domains': ['Building'], 'ranges': ['Address']}}
        )
        
        self.guidelines_context = GuidelinesContext(
            property_definitions={'name': {'description': 'Building name'}},
            validation_rules={'name': {'required': True}},
            required_properties={'Building': ['name']},
            property_types={'name': 'string'},
            property_units={}
        )
        
        self.enhanced_context = EnhancedTTLContext(
            graph=self.mock_graph,
            file_path='test.ttl',
            basic_analysis='Test analysis',
            ontology_context=self.ontology_context,
            guidelines_context=self.guidelines_context
        )
    
    def test_list_input_error_case(self):
        """Test the exact error case: list input causing 'list' object has no attribute 'get'"""
        
        # This is the exact input that was causing the error
        problematic_input = [{'buildingCount': '137'}]
        
        mock_ctx = Mock()
        mock_ctx.deps = self.enhanced_context
        
        async def test_async():
            # This should NOT crash with "'list' object has no attribute 'get'"
            result = await analyze_contextual_results(
                mock_ctx,
                problematic_input,  # This was the problematic input
                "How many buildings are there?",
                "Test ontology context",
                "Test guidelines context"
            )
            return result
        
        # Run the async test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async())
            
            # Verify it returns a proper ContextualInsight
            self.assertIsInstance(result, ContextualInsight)
            
            # Verify it contains the count information
            self.assertIn('1', result.insight)
            
            # Verify it has reasonable confidence
            self.assertGreater(result.confidence, 0.0)
            
            # Verify it has supporting data
            self.assertIsNotNone(result.supporting_data)
            
            print(f"✅ Test passed! Result: {result.insight}")
            
        finally:
            loop.close()
    
    def test_various_problematic_inputs(self):
        """Test various inputs that could cause similar errors"""
        
        mock_ctx = Mock()
        mock_ctx.deps = self.enhanced_context
        
        problematic_inputs = [
            # List with dict
            [{'count': '5'}],
            # List with multiple dicts
            [{'count': '5'}, {'name': 'test'}],
            # List with non-dict items
            ['string_item'],
            # Empty list
            [],
            # List with None
            [None],
            # List with mixed types
            [{'count': '5'}, 'string', None],
        ]
        
        async def test_input(input_data):
            try:
                result = await analyze_contextual_results(
                    mock_ctx,
                    input_data,
                    "Test question",
                    "Test ontology context",
                    "Test guidelines context"
                )
                return result, None
            except Exception as e:
                return None, e
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            for i, input_data in enumerate(problematic_inputs):
                result, error = loop.run_until_complete(test_input(input_data))
                
                if error:
                    self.fail(f"Input {i} ({input_data}) caused error: {error}")
                
                self.assertIsInstance(result, ContextualInsight)
                print(f"✅ Input {i} handled successfully: {type(input_data).__name__}")
                
        finally:
            loop.close()
    
    def test_edge_case_inputs(self):
        """Test edge case inputs that might cause attribute errors"""
        
        mock_ctx = Mock()
        mock_ctx.deps = self.enhanced_context
        
        edge_cases = [
            # String that looks like JSON but isn't
            "{'not': 'valid json'}",
            # Invalid JSON string
            '{"incomplete": json',
            # Number
            42,
            # Boolean
            True,
            # None
            None,
            # Complex nested structure
            {'data': [{'nested': {'deep': 'value'}}]},
        ]
        
        async def test_input(input_data):
            try:
                result = await analyze_contextual_results(
                    mock_ctx,
                    input_data,
                    "Test question",
                    "Test ontology context",
                    "Test guidelines context"
                )
                return result, None
            except Exception as e:
                return None, e
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            for i, input_data in enumerate(edge_cases):
                result, error = loop.run_until_complete(test_input(input_data))
                
                if error:
                    self.fail(f"Edge case {i} ({type(input_data).__name__}: {input_data}) caused error: {error}")
                
                self.assertIsInstance(result, ContextualInsight)
                print(f"✅ Edge case {i} handled successfully: {type(input_data).__name__}")
                
        finally:
            loop.close()

if __name__ == '__main__':
    print("🧪 Testing specific error case: 'list' object has no attribute 'get'")
    print("=" * 70)
    
    unittest.main(verbosity=2)
    
    print("\n" + "=" * 70)
    print("✅ All tests passed! The error case has been fixed.")
